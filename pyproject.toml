[project]
name = "Eko"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles==24.1.0",
    "aiohappyeyeballs==2.4.4",
    "aiohttp==3.11.11",
    "aiosignal==1.3.2",
    "altair==5.5.0",
    "annotated-types==0.7.0",
    "argon2-cffi==23.1.0",
    "argon2-cffi-bindings==21.2.0",
    "attrs==24.3.0",
    "bcrypt==4.2.1",
    "blinker==1.9.0",
    "brotli==1.1.0",
    "cachetools==5.5.2",
    "certifi==2024.12.14",
    "cffi==1.17.1",
    "charset-normalizer==3.4.1",
    "click==8.1.8",
    "coloredlogs==15.0.1",
    "colorlog==6.9.0",
    "cssselect2==0.8.0",
    "dataclasses-json==0.6.7",
    "decorator==5.1.1",
    "deprecated==1.2.15",
    "dirtyjson==1.0.8",
    "distro==1.9.0",
    "dnspython==2.7.0",
    "email-validator==2.2.0",
    "fastapi==0.115.6",
    "filetype==1.2.0",
    "fonttools==4.56.0",
    "frozenlist==1.5.0",
    "fsspec==2024.12.0",
    "gitdb==4.0.12",
    "gitpython==3.1.44",
    "google-ai-generativelanguage==0.6.15",
    "google-api-core==2.24.1",
    "google-api-python-client==2.161.0",
    "google-auth==2.38.0",
    "google-auth-httplib2==0.2.0",
    "google-generativeai==0.8.4",
    "googleapis-common-protos==1.68.0",
    "greenlet==3.1.1",
    "grpcio==1.70.0",
    "grpcio-status==1.70.0",
    "grpcio-tools==1.70.0",
    "gunicorn>=23.0.0",
    "h11==0.14.0",
    "h2==4.2.0",
    "hdbscan==0.8.40",
    "hpack==4.1.0",
    "httpcore==1.0.7",
    "httplib2==0.22.0",
    "httpx==0.28.1",
    "humanfriendly==10.0",
    "hyperframe==6.1.0",
    "idna==3.10",
    "jinja2==3.1.6",
    "jiter==0.8.2",
    "joblib==1.4.2",
    "jsonpatch==1.33",
    "jsonpointer==3.0.0",
    "jsonschema==4.23.0",
    "jsonschema-specifications==2024.10.1",
    "langchain==0.3.15",
    "langchain-core==0.3.31",
    "langchain-text-splitters==0.3.5",
    "langsmith==0.2.11",
    "llama-cloud==0.1.9",
    "llama-index==0.12.12",
    "llama-index-agent-openai==0.4.2",
    "llama-index-cli==0.4.0",
    "llama-index-core==0.12.12",
    "llama-index-embeddings-openai==0.3.1",
    "llama-index-indices-managed-llama-cloud==0.6.3",
    "llama-index-llms-gemini==0.4.10",
    "llama-index-llms-openai==0.3.13",
    "llama-index-multi-modal-llms-openai==0.4.2",
    "llama-index-program-openai==0.3.1",
    "llama-index-question-gen-openai==0.3.0",
    "llama-index-readers-file==0.4.3",
    "llama-index-readers-llama-parse==0.4.0",
    "llama-index-vector-stores-qdrant==0.4.3",
    "llama-parse==0.5.19",
    "llvmlite==0.44.0",
    "lxml==5.3.1",
    "markupsafe==3.0.2",
    "marshmallow==3.25.1",
    "minio==7.2.15",
    "multidict==6.1.0",
    "mypy-extensions==1.0.0",
    "narwhals==1.30.0",
    "nest-asyncio==1.6.0",
    "networkx==3.4.2",
    "nltk==3.9.1",
    "numba==0.61.0",
    "openai==1.59.9",
    "orjson==3.10.15",
    "packaging==24.2",
    "pandas==2.2.3",
    "passlib==1.7.4",
    "pdfkit==1.0.0",
    "pillow==10.4.0",
    "plum-dispatch==1.7.4",
    "portalocker==2.10.1",
    "propcache==0.2.1",
    "proto-plus==1.26.0",
    "protobuf==5.29.3",
    "py==1.11.0",
    "pyarrow==19.0.1",
    "pyasn1==0.6.1",
    "pyasn1-modules==0.4.1",
    "pycparser==2.22",
    "pycryptodome==3.21.0",
    "pydantic[email]==2.10.5",
    "pydantic-core==2.27.2",
    "pydeck==0.9.1",
    "pydyf==0.11.0",
    "pyjwt==2.10.1",
    "pymongo==4.10.1",
    "pymupdf==1.25.2",
    "pynndescent==0.5.13",
    "pyparsing==3.2.1",
    "pypdf==5.1.0",
    "pyphen==0.17.2",
    "python-dateutil==2.9.0.post0",
    "python-docx==1.1.2",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.20",
    "pytz==2024.2",
    "pyyaml==6.0.2",
    "qdrant-client==1.13.2",
    "referencing==0.36.2",
    "regex==2024.11.6",
    "requests==2.32.3",
    "requests-toolbelt==1.0.0",
    "retry==0.9.2",
    "rpds-py==0.23.1",
    "rsa==4.9",
    "scikit-learn==1.6.1",
    "scipy==1.15.1",
    "shutup==0.2.0",
    "six==1.17.0",
    "smmap==5.0.2",
    "sniffio==1.3.1",
    "soupsieve==2.6",
    "spire-doc==13.1.0",
    "sqlalchemy==2.0.37",
    "starlette==0.41.3",
    "streamlit==1.43.1",
    "striprtf==0.0.26",
    "tabulate>=0.9.0",
    "tenacity==9.0.0",
    "threadpoolctl==3.5.0",
    "tiktoken==0.8.0",
    "tinycss2==1.4.0",
    "tinyhtml5==2.0.0",
    "toml==0.10.2",
    "tornado==6.4.2",
    "tqdm==4.67.1",
    "typing-extensions==4.12.2",
    "typing-inspect==0.9.0",
    "tzdata==2024.2",
    "umap-learn==0.5.7",
    "uritemplate==4.1.1",
    "urllib3==2.3.0",
    "uvicorn==0.34.0",
    "watchdog==6.0.0",
    "webencodings==0.5.1",
    "websockets==14.2",
    "wrapt==1.17.2",
    "yarl==1.18.3",
    "zopfli==0.2.3.post1",
    "ma-nish>=1.3.2",
    "twilio>=9.5.1",
    "ipykernel>=6.29.5",
    "phonenumbers>=9.0.5",
    "matplotlib>=3.10.3",
    "selectolax>=0.3.30",
    "anyio==3.6.2",
]

