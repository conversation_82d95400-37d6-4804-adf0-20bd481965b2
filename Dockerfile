# Stage 0: Ultra-minimal base with only essential system packages
FROM python:3.11-slim AS base

# Install only absolutely necessary system dependencies in one layer
RUN apt-get update && apt-get install -y --no-install-recommends \
    # Essential build tools (minimal set)
    gcc \
    libc6-dev \
    # PDF generation essentials only
    wkhtmltopdf \
    xvfb \
    # Minimal font support
    fonts-liberation \
    # Network tools
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy uv binaries from upstream image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set working directory
WORKDIR /app

# Stage 1: Dependencies with aggressive optimization
FROM base AS dependencies

# Copy only lockfiles first to cache dependency install
COPY pyproject.toml uv.lock ./

# Install dependencies with maximum optimization and persistent cache
ENV UV_HTTP_TIMEOUT=60 \
    UV_CACHE_DIR=/app/.uv-cache \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8

# Use cache mount for faster rebuilds
RUN --mount=type=cache,target=/app/.uv-cache \
    uv sync --frozen --no-dev \
    && find /app -name "*.pyc" -delete \
    && find /app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# Stage 2: Pre-download NLTK data (optimized)
FROM dependencies AS nltk-data

# Pre-download essential NLTK packages to avoid startup delays
RUN uv run python -c "import nltk; \
    import os; \
    nltk_data_dir = '/app/nltk_data'; \
    os.makedirs(nltk_data_dir, exist_ok=True); \
    nltk.data.path.insert(0, nltk_data_dir); \
    packages = ['punkt', 'punkt_tab', 'wordnet', 'omw-1.4', 'stopwords', 'averaged_perceptron_tagger']; \
    [nltk.download(pkg, download_dir=nltk_data_dir, quiet=True) for pkg in packages]; \
    print('Essential NLTK packages downloaded')" && \
    # Clean up any temporary files
    find /app -name "*.pyc" -delete && \
    find /app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# Stage 3: Final ultra-lightweight runtime
FROM dependencies AS runtime

# Copy NLTK data from previous stage
COPY --from=nltk-data /app/nltk_data /app/nltk_data

# Set NLTK data path environment variable
ENV NLTK_DATA=/app/nltk_data

# Copy application code (done last to maximize caching)
COPY . .

# Remove unnecessary files to reduce image size
RUN find . -name "*.pyc" -delete && \
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.pyo" -delete && \
    find . -name "*.pyd" -delete && \
    find . -name ".git" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.log" -delete && \
    rm -rf /tmp/* /var/tmp/* && \
    # Remove test files and documentation
    find . -name "*test*" -type f -delete 2>/dev/null || true

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Health check with minimal overhead
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=2 \
    CMD curl -f http://localhost:8000/health-minimal || exit 1

EXPOSE 8000

# Optimized startup command with immediate availability
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]
